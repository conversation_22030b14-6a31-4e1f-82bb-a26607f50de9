<!-- 顶部标题 -->
<template>
  <header class="header">
    <!-- 左侧天气 -->
    <div class="left-section">
      <div class="weather-info">
        <img src="@/assets/img/Un.png" alt="天气" class="weather-icon" />
        <span class="weather-text">晴天 25°C</span>
      </div>
    </div>

    <!-- 中间标题 -->
    <div class="title">智能化零碳楼宇管理平台驾驶仓</div>

    <!-- 右侧日期时间和开关 -->
    <div class="right-section">
      <div class="datetime-info">
        <div class="date">{{ currentDate }}</div>
        <div class="time">{{ currentTime }}</div>
      </div>
      <div class="switch-container" @click="toggleSwitch">
        <img src="@/assets/img/开关.png" alt="开关" class="switch-icon" :class="{ active: switchStatus }" />
      </div>
    </div>
  </header>

  <!-- 图片展示区域 -->
  <div class="image-display">
    <div class="display-item">
      <div class="icon-container">
        <img src="@/assets/img/sd_icon.png" alt="园区总能耗图标" class="icon" />
      </div>
      <div class="content">
        <span class="label">园区总能耗</span>
        <span class="value">{{ energyData.park }} kwh</span>
      </div>
    </div>
    <div class="display-item">
      <div class="icon-container">
        <img src="@/assets/img/sd_icon.png" alt="单位面积能耗图标" class="icon" />
      </div>
      <div class="content">
        <span class="label">单位面积能耗</span>
        <span class="value">{{ energyData.unit }} kwh/m³</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { getEnergyData, type EnergyDataItem } from '@/api/index'

// 能耗数据
const energyData = ref<EnergyDataItem>({
  park: '0',
  unit: '0'
})

// 日期时间相关
const currentDate = ref('')
const currentTime = ref('')
let timeInterval: number | null = null

// 开关状态
const switchStatus = ref(false)

// 格式化日期时间
const formatDateTime = () => {
  const now = new Date()

  // 格式化日期 (YYYY-MM-DD)
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  currentDate.value = `${year}-${month}-${day}`

  // 格式化时间 (HH:MM:SS)
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`
}

// 开关切换
const toggleSwitch = () => {
  switchStatus.value = !switchStatus.value
  console.log('开关状态:', switchStatus.value ? '开启' : '关闭')
}

// 获取能耗数据
const fetchEnergyData = async () => {
  try {
    const response = await getEnergyData()
    console.log('获取能耗数据成功:', response)
    if (  response.data && response.data.data) {
      console.log('能耗数据:', response.data.data)
      energyData.value = response.data.data
    }
  } catch (error) {
    console.error('获取能耗数据失败:', error)
  }
}

// 组件挂载时获取数据和启动定时器
onMounted(() => {
  fetchEnergyData()

  // 初始化时间显示
  formatDateTime()

  // 每秒更新时间
  timeInterval = setInterval(formatDateTime, 1000)
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="scss" scoped>
.header {
  position: absolute;
  margin: 0 12px;
  width: calc(100% - 24px);
  height: 87px;
  background: url('@/assets/img/head_bg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  color: rgba(230, 239, 253);
}

.left-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.weather-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.weather-text {
  font-size: 16px;
  font-weight: 500;
  color: rgba(230, 239, 253, 0.9);
}

.title {
  flex: 1;
  text-align: center;
  font-size: 25px;
  font-weight: 800;
  letter-spacing: 12px;
  color: rgba(230, 239, 253);
}

.right-section {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
  justify-content: flex-end;
}

.datetime-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.date {
  font-size: 14px;
  font-weight: 500;
  color: rgba(230, 239, 253, 0.8);
}

.time {
  font-size: 16px;
  font-weight: 600;
  color: #00ffff;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
}

.switch-container {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}

.switch-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
  transition: all 0.3s ease;
  filter: brightness(0.8);

  &.active {
    filter: brightness(1.2) drop-shadow(0 0 10px rgba(0, 255, 255, 0.6));
  }
}

.image-display {
  position: absolute;
  top: 120px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 100px;
  align-items: center;
  justify-content: center;
}

.display-item {
  display: flex;
  align-items: center;
  position: relative;
  width: 350px;
  height: 100px;
  padding-left: 10px;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
  flex-shrink: 0;
}

.icon {
  width: 35px;
  height: 35px;
  object-fit: contain;
}

.content {
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('@/assets/img/sd_bottom.png') no-repeat center center;
  background-size: 100% 100%;
  gap: 3px;
  color: #fff;
  flex: 1;
  margin-left: -13px;
}

.label {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.value {
  font-size: 24px;
  font-weight: 600;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}
</style>
